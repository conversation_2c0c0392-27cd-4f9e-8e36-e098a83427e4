"""表情包生成命令处理器"""

from astrbot.core.platform import AstrMessageEvent
import astrbot.core.message.components as Comp
from ..core import MemeManager


class GenerationHandler:
    """表情包生成命令处理器"""
    
    def __init__(self, meme_manager: MemeManager):
        self.meme_manager = meme_manager
    
    async def handle_generate_meme(self, event: AstrMessageEvent):
        """
        处理表情包生成请求
        
        Args:
            event: 消息事件
        """
        try:
            image = await self.meme_manager.generate_meme(event)
            if image:
                chain = [Comp.Image.fromBytes(image)]
                yield event.chain_result(chain)
        except Exception as e:
            # 这里可以根据需要记录错误日志
            # logger.error(f"表情包生成失败: {e}")
            pass  # 静默失败，不响应用户
