<div align="center">

![:name](https://count.getloli.com/@astrbot_plugin_meme_generator?name=astrbot_plugin_meme_generator&theme=gelbooru&padding=8&offset=0&align=top&scale=1&pixelated=0&darkmode=auto)

# 🎨 AstrBot 表情包生成器

_✨ 基于 [AstrBot](https://github.com/AstrBotDevs/AstrBot) 的智能表情包创作工具 ✨_

[![License](https://img.shields.io/badge/License-Apache%202.0-red.svg)](https://opensource.org/licenses/Apache-2.0)
[![Python 3.11+](https://img.shields.io/badge/Python-3.11%2B-brightgreen.svg)](https://www.python.org/)
[![AstrBot](https://img.shields.io/badge/AstrBot-3.5%2B-purple.svg)](https://github.com/Soulter/AstrBot)
[![GitHub](https://img.shields.io/badge/开发者-SodaSizzle-orange)](http://127.0.0.1:3000/SodaSizzle)

</div>

## 🚀 项目简介

这是一个为 AstrBot 量身定制的**智能表情包生成插件**，基于 [meme-generator-rs](https://github.com/MemeCrafters/meme-generator-rs) 和 [nonebot-plugin-memes](https://github.com/MemeCrafters/nonebot-plugin-memes) 开发，采用先进的图像处理算法和模板匹配技术，为用户提供丰富多样的表情包创作体验。

### ✨ 核心特性

- 🎯 **智能关键词识别** - 支持模糊匹配和精确匹配两种模式
- 🖼️ **多源图片支持** - 自动获取用户头像、支持上传图片、引用消息图片
- ⚡ **高性能渲染** - 基于 Rust 底层引擎，生成速度极快
- 🎨 **丰富模板库** - 内置 200+ 精选表情包模板
- 🔧 **灵活配置** - 支持黑名单管理、图片压缩、超时控制等
- 📱 **跨平台兼容** - 完美适配各种聊天平台

## 📦 快速安装

### 方式一：自动安装（推荐）

1. 打开 AstrBot 管理面板
2. 进入插件市场，搜索 `astrbot_plugin_meme_generator`
3. 点击安装，等待自动完成

### 方式二：手动部署

```bash
# 进入插件目录
cd /path/to/AstrBot/data/plugins

# 克隆项目
git clone http://127.0.0.1:3000/SodaSizzle/astrbot_plugin_meme_generator

# 安装依赖
pip install -r astrbot_plugin_meme_generator/requirements.txt
```

## 🔧 故障排除

### Linux 下字体异常问题

如果在 Linux 系统下遇到字体渲染异常或乱码问题，请尝试以下解决方案：

设置 locate 为英文：
```bash
export LANG=en_US.UTF-8
```
设置环境变量后，重启 AstrBot 服务以使配置生效。


### 其他常见问题

- **生成失败**：检查网络连接和依赖包安装
- **权限错误**：确保用户具有管理员权限
- **模板加载失败**：检查 `meme_generator` 包是否正确安装

> 💡 **提示**: 启动后会异步后台下载字体和模板资源，请耐心等待。

## ⚙️ 配置说明

### 缓存系统说明

- **缓存位置**: `data/cache/meme_avatars/`
- **缓存文件**: 用户头像以MD5哈希命名存储
- **元数据**: `metadata.json` 记录缓存时间戳
- **自动清理**: 每6小时自动清理过期缓存

表情帮助
![img.png](static/picture/list.png)

表情包使用说明
![img.png](static/picture/help.png)

表情包状态
![img.png](static/picture/info.png)

## 🐔 使用说明

- 本插件支持从原始消息中提取参数，请用空格隔开参数，如 “喜报 nmsl”
- 本插件支持从引用消息中提取参数，如“[引用的消息] 喜报”
- 提供的参数不够时，插件自动获取消息发送者、被 @ 的用户以及 bot 自身的相关参数来补充。

## 📌 注意事项

- 缓存数据存储在 `data/cache/meme_avatars/` 目录中，可定期清理



## 👥 贡献指南

- 🌟 Star 这个项目！（点右上角的星星，感谢支持！）
- 🐛 提交 Issue 报告问题
- 💡 提出新功能建议
- 🔧 提交 Pull Request 改进代码

## 🔗 相关项目

- [meme-generator-rs](https://github.com/MemeCrafters/meme-generator-rs) - 核心生成引擎（Rust 实现）
- [nonebot-plugin-memes](https://github.com/MemeCrafters/nonebot-plugin-memes) - NoneBot 表情包插件（参考实现）
- [AstrBot](https://astrbot.app/) - 多平台聊天机器人框架
- [SodaSizzle's Projects](http://127.0.0.1:3000/SodaSizzle) - 更多有趣的项目

## 📄 许可证

本项目采用 Apache 2.0 许可证，详见 [LICENSE](LICENSE) 文件。

---

<div align="center">

**🎉 感谢使用 AstrBot 表情包生成器！**

如果觉得好用，请给个 ⭐ Star 支持一下！

</div>
