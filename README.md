<div align="center">

# 🎭 AstrBot 表情包生成器插件

_✨ 高性能智能表情包生成器 - 让聊天更有趣 ✨_

[![Version](https://img.shields.io/badge/version-v1.0.0-blue?style=for-the-badge&logo=github)](https://github.com/SodaSizzle/astrbot_plugin_meme_generator)
[![Python](https://img.shields.io/badge/python-3.10+-blue?style=for-the-badge&logo=python)](https://www.python.org/)
[![License](https://img.shields.io/badge/license-MIT-green?style=for-the-badge&logo=opensourceinitiative)](LICENSE)
[![AstrBot](https://img.shields.io/badge/AstrBot-Plugin-purple?style=for-the-badge&logo=robot)](https://github.com/Soulter/AstrBot)

[![Stars](https://img.shields.io/github/stars/SodaSizzle/astrbot_plugin_meme_generator?style=social)](https://github.com/SodaSizzle/astrbot_plugin_meme_generator/stargazers)
[![Forks](https://img.shields.io/github/forks/SodaSizzle/astrbot_plugin_meme_generator?style=social)](https://github.com/SodaSizzle/astrbot_plugin_meme_generator/network/members)
[![Issues](https://img.shields.io/github/issues/SodaSizzle/astrbot_plugin_meme_generator?style=social)](https://github.com/SodaSizzle/astrbot_plugin_meme_generator/issues)

</div>

---

## 🌟 项目简介

这是一个为 **AstrBot** 量身定制的**智能表情包生成插件**，基于 [meme-generator-rs](https://github.com/MemeCrafters/meme-generator-rs) 和 [nonebot-plugin-memes](https://github.com/MemeCrafters/nonebot-plugin-memes) 开发。

采用先进的图像处理算法和模板匹配技术，结合 **Rust 高性能引擎**，为用户提供丰富多样的表情包创作体验。无论是日常聊天还是群组互动，都能让你的对话更加生动有趣！

### 🎯 设计理念

- **🚀 高性能优先** - 基于 Rust 引擎，毫秒级生成速度
- **🧠 智能识别** - 先进的关键词匹配算法，理解用户意图
- **🎨 丰富创意** - 200+ 精选模板，覆盖各种场景和情感
- **⚡ 即开即用** - 零配置启动，开箱即用的用户体验
- **🔧 灵活定制** - 完善的配置系统，满足不同需求

## ✨ 核心特性

<table>
<tr>
<td width="50%">

### 🎯 智能识别
- **模糊匹配** - 智能理解用户意图
- **精确匹配** - 支持关键词精确触发
- **多语言支持** - 中英文关键词识别
- **上下文理解** - 结合聊天场景智能推荐

### 🖼️ 多源图片
- **自动头像** - 智能获取用户头像
- **上传图片** - 支持用户自定义图片
- **引用消息** - 快速使用聊天中的图片
- **格式兼容** - 支持 JPG/PNG/GIF 等格式

</td>
<td width="50%">

### ⚡ 高性能引擎
- **Rust 底层** - 毫秒级生成速度
- **异步处理** - 支持高并发请求
- **内存优化** - 智能内存管理
- **资源控制** - 完善的超时和限流机制

### 🎨 丰富模板
- **200+ 模板** - 覆盖各种场景
- **实时更新** - 持续添加新模板
- **分类管理** - 按场景和情感分类
- **自定义禁用** - 灵活的模板管理

</td>
</tr>
</table>

### 🔧 高级功能

- 💾 **智能缓存系统** - 头像缓存机制，显著提升生成速度
- ⏱️ **冷却控制机制** - 防止刷屏，保护服务器资源
- 📱 **跨平台兼容** - 完美适配 QQ、微信、Telegram 等平台
- 🛡️ **权限管理** - 细粒度的用户权限控制
- 📊 **统计监控** - 详细的使用统计和性能监控
- 🔄 **自动恢复** - 完善的错误处理和自动恢复机制

## 📦 快速安装

<div align="center">

### 🚀 一键安装（推荐）

</div>

<table>
<tr>
<td width="50%">

#### 方式一：插件市场安装
```bash
# 1. 打开 AstrBot 管理面板
# 2. 进入插件市场
# 3. 搜索 "meme_generator"
# 4. 点击安装，等待完成
```

**优势：**
- ✅ 自动处理依赖
- ✅ 版本管理
- ✅ 一键更新

</td>
<td width="50%">

#### 方式二：命令行安装
```bash
# 进入 AstrBot 目录
cd /path/to/AstrBot

# 使用插件管理命令
python main.py plugin get \
  http://127.0.0.1:3000/SodaSizzle/astrbot_plugin_meme_generator
```

**优势：**
- ✅ 命令行操作
- ✅ 批量部署
- ✅ 脚本自动化

</td>
</tr>
</table>

### 🛠️ 手动部署（开发者）

```bash
# 1. 进入插件目录
cd /path/to/AstrBot/data/plugins

# 2. 克隆项目
git clone http://127.0.0.1:3000/SodaSizzle/astrbot_plugin_meme_generator

# 3. 安装依赖
cd astrbot_plugin_meme_generator
pip install -r requirements.txt

# 4. 重启 AstrBot
# 插件将自动加载并初始化
```

### 🔧 初始化配置

插件首次启动时会自动执行以下操作：

<div align="center">

| 步骤 | 操作 | 预计时间 | 说明 |
|------|------|----------|------|
| 1️⃣ | 下载字体文件 | 30-60s | 中文字体支持 |
| 2️⃣ | 初始化模板资源 | 60-120s | 表情包模板库 |
| 3️⃣ | 创建配置文件 | <5s | 插件配置初始化 |
| 4️⃣ | 缓存目录创建 | <5s | 头像缓存系统 |

</div>

> 💡 **首次启动提示**:
> - 首次启动可能需要 **2-3 分钟** 下载资源
> - 请确保网络连接稳定
> - 下载完成后后续启动将非常快速

### ⚠️ 字体问题解决

<details>
<summary>🔧 <strong>点击展开字体问题解决方案</strong></summary>

如果遇到表情包中文字显示异常，通常是字体缺失导致。请根据你的系统选择对应解决方案：

<table>
<tr>
<td width="33%">

#### 🪟 Windows 系统
```bash
# 系统通常已内置中文字体
# 如有问题，请安装以下字体：
# - 微软雅黑 (Microsoft YaHei)
# - 宋体 (SimSun)
# - 黑体 (SimHei)

# 字体安装路径：
# C:\Windows\Fonts\
```

</td>
<td width="33%">

#### 🐧 Linux 系统
```bash
# Ubuntu/Debian
sudo apt-get update
sudo apt-get install -y \
  fonts-noto-cjk \
  fonts-wqy-zenhei \
  fonts-wqy-microhei

# CentOS/RHEL/Fedora
sudo yum install -y \
  google-noto-cjk-fonts \
  wqy-zenhei-fonts \
  wqy-microhei-fonts

# Arch Linux
sudo pacman -S \
  noto-fonts-cjk \
  wqy-zenhei \
  wqy-microhei
```

</td>
<td width="33%">

#### 🐳 Docker 环境
```dockerfile
# 在 Dockerfile 中添加
FROM python:3.10-slim

# 安装中文字体
RUN apt-get update && \
    apt-get install -y \
    fonts-noto-cjk \
    fonts-wqy-zenhei && \
    rm -rf /var/lib/apt/lists/*

# 其他配置...
```

**Docker Compose:**
```yaml
services:
  astrbot:
    volumes:
      - /usr/share/fonts:/usr/share/fonts:ro
```

</td>
</tr>
</table>

#### 🔍 字体问题诊断

```bash
# 检查系统已安装的中文字体
fc-list :lang=zh

# 测试字体渲染
python -c "
from PIL import Image, ImageDraw, ImageFont
try:
    font = ImageFont.truetype('NotoSansCJK-Regular.ttc', 20)
    print('✅ 字体加载成功')
except:
    print('❌ 字体加载失败，请安装中文字体')
"
```

> 🔧 **快速修复**: 安装字体后请重启 AstrBot，插件会自动检测并使用新安装的字体。

</details>

## ⚙️ 配置说明

<div align="center">

### 🎛️ 插件配置面板

</div>

插件支持丰富的配置选项，可在 **AstrBot 管理面板** 中进行可视化调整：

<table>
<tr>
<td width="50%">

#### 🔧 基础配置

| 配置项 | 类型 | 默认值 | 说明 |
|--------|------|--------|------|
| `enable_plugin` | `bool` | `true` | 🔌 全局插件开关 |
| `cooldown_seconds` | `int` | `3` | ⏱️ 用户冷却时间(秒) |
| `generation_timeout` | `int` | `30` | ⏰ 生成超时时间(秒) |

#### 📊 性能配置

| 配置项 | 类型 | 默认值 | 说明 |
|--------|------|--------|------|
| `enable_avatar_cache` | `bool` | `true` | 💾 启用头像缓存 |
| `cache_expire_hours` | `int` | `24` | 🕐 缓存过期时间(小时) |
| `disabled_templates` | `list` | `[]` | 🚫 禁用模板列表 |

</td>
<td width="50%">

#### 🎯 配置建议

**高性能场景:**
```json
{
  "cooldown_seconds": 1,
  "generation_timeout": 15,
  "enable_avatar_cache": true,
  "cache_expire_hours": 48
}
```

**资源节约场景:**
```json
{
  "cooldown_seconds": 5,
  "generation_timeout": 60,
  "enable_avatar_cache": false,
  "cache_expire_hours": 12
}
```

**群聊优化场景:**
```json
{
  "cooldown_seconds": 2,
  "generation_timeout": 20,
  "enable_avatar_cache": true,
  "cache_expire_hours": 72
}
```

</td>
</tr>
</table>

### 💾 缓存系统详解

<div align="center">

| 组件 | 路径/配置 | 功能说明 |
|------|-----------|----------|
| 📁 **缓存目录** | `data/cache/meme_avatars/` | 存储用户头像缓存文件 |
| 🗂️ **缓存文件** | `{MD5_HASH}.jpg/png` | 用户头像以MD5哈希命名 |
| 📋 **元数据** | `metadata.json` | 记录缓存时间戳和统计信息 |
| 🔄 **自动清理** | 每6小时执行 | 自动清理过期缓存文件 |

</div>

#### 缓存性能优化

- **命中率提升**: 智能预加载常用用户头像
- **存储优化**: 自动压缩和格式转换
- **内存管理**: LRU算法管理内存缓存
- **并发安全**: 支持多线程安全访问

## 📋 命令大全

<div align="center">

### 🎮 用户命令 | 🔧 管理员命令

</div>

<table>
<tr>
<td width="50%">

### 👥 普通用户命令

<div align="center">

![表情帮助命令效果图](./docs/images/meme_help_demo.png)

_✨ 表情帮助 - 查看插件完整功能菜单 ✨_

</div>

| 🎯 命令 | 📝 功能描述 | 💡 使用示例 |
|---------|-------------|-------------|
| `表情帮助` | 📖 查看插件帮助菜单 | `表情帮助` `meme帮助` |
| `表情列表` | 📋 查看所有可用模板 | `表情列表` `meme列表` |
| `表情信息 <关键词>` | 🔍 查看模板详细信息 | `表情信息 摸头` |
| `<关键词> [参数]` | 🎨 生成表情包 | `摸头 @某人` |

<div align="center">

![表情列表命令效果图](./docs/images/meme_list_demo.png)

_✨ 表情列表 - 浏览所有可用的表情包模板 ✨_

</div>

#### 🎨 生成示例

```
摸头 @用户名          # 摸头表情包
举牌 你好世界         # 文字表情包
拍拍 @用户1 @用户2    # 多人表情包
亲亲                 # 使用自己头像
```

</td>
<td width="50%">

### 🛡️ Bot管理员命令

<div align="center">

![表情状态命令效果图](./docs/images/meme_status_demo.png)

_✨ 表情状态 - 查看插件详细运行状态和统计信息 ✨_

</div>

| 🔧 命令 | 📝 功能描述 | 💡 使用示例 |
|---------|-------------|-------------|
| `表情启用` | ✅ 启用整个插件功能 | `表情启用` `meme启用` |
| `表情禁用` | ❌ 禁用整个插件功能 | `表情禁用` `meme禁用` |
| `表情状态` | 📊 查看插件详细统计 | `表情状态` `meme状态` |
| `单表情禁用 <模板>` | 🚫 禁用指定模板 | `单表情禁用 摸头` |
| `单表情启用 <模板>` | ✅ 启用指定模板 | `单表情启用 摸头` |
| `禁用列表` | 📋 查看禁用模板列表 | `禁用列表` |

#### 🛠️ 管理示例

```bash
# 插件管理
表情禁用              # 临时关闭插件
表情启用              # 重新启用插件

# 模板管理
单表情禁用 不合适的模板  # 禁用特定模板
单表情启用 摸头         # 重新启用模板

# 状态监控
表情状态              # 查看详细状态
禁用列表              # 查看被禁用的模板
```

</td>
</tr>
</table>

### 🎯 命令权限说明

- **👥 普通用户**: 可使用所有生成和查询命令
- **🛡️ Bot管理员**: 拥有完整的插件管理权限
- **🔒 权限检查**: 自动识别用户身份，确保安全性


## 🎯 快速上手指南

<div align="center">

### 🚀 三分钟上手表情包生成

</div>

<table>
<tr>
<td width="33%">

#### 1️⃣ 基础查询
```bash
# 查看帮助
表情帮助

# 浏览模板
表情列表

# 查看模板详情
表情信息 摸头
```

**学会这些，你就能：**
- 📖 了解所有功能
- 🎨 找到心仪的模板
- 🔍 掌握使用方法

</td>
<td width="33%">

#### 2️⃣ 生成表情包
```bash
# 单人表情包
摸头 @用户名
亲亲 @好友

# 文字表情包
举牌 你好世界
打call 加油

# 多人表情包
拍拍 @用户1 @用户2
```

**掌握这些，你就能：**
- 🎭 制作个性表情包
- 💬 让聊天更有趣
- 🎉 活跃群组氛围

</td>
<td width="33%">

#### 3️⃣ 高级管理
```bash
# 插件管理
表情状态
表情禁用
表情启用

# 模板管理
单表情禁用 模板名
禁用列表
```

**管理员专享：**
- 📊 监控插件状态
- 🛡️ 管理模板权限
- ⚙️ 优化使用体验

</td>
</tr>
</table>

### 🎮 实战演练

<details>
<summary>💡 <strong>点击查看详细使用场景</strong></summary>

#### 🌟 日常聊天场景

```bash
# 表达情感
摸头 @小可爱        # 安慰朋友
拍拍 @队友         # 鼓励队友
亲亲 @恋人         # 表达爱意

# 搞笑互动
举牌 今天也要加油哦   # 正能量
打call 你最棒      # 支持朋友
```

#### 🎉 群组活动场景

```bash
# 欢迎新人
举牌 欢迎新朋友
拍拍 @新成员

# 活跃气氛
摸头 @群主
亲亲 大家好

# 节日祝福
举牌 新年快乐
打call 2024加油
```

#### 🛠️ 管理维护场景

```bash
# 日常检查
表情状态           # 查看运行状态
禁用列表           # 检查禁用模板

# 问题处理
单表情禁用 不合适模板  # 临时禁用
表情禁用           # 紧急关闭
表情启用           # 恢复服务
```

</details>

## 🔧 技术架构

<div align="center">

### 🏗️ 系统架构图

</div>

```mermaid
graph TB
    A[用户消息] --> B[AstrBot 框架]
    B --> C[Meme Generator 插件]
    C --> D{消息类型判断}

    D -->|命令消息| E[命令处理器]
    D -->|关键词消息| F[生成处理器]

    E --> G[模板管理器]
    E --> H[管理员处理器]

    F --> I[参数收集器]
    I --> J[图像生成器]

    J --> K[meme-generator-rs]
    K --> L[表情包输出]

    G --> M[模板缓存]
    I --> N[头像缓存]

    style A fill:#e1f5fe
    style L fill:#c8e6c9
    style K fill:#ffecb3
```

### 🧩 核心组件

<table>
<tr>
<td width="50%">

#### 🎯 核心依赖

| 组件 | 版本 | 作用 |
|------|------|------|
| **[meme-generator-rs](https://github.com/MemeCrafters/meme-generator-rs)** | `latest` | 🦀 Rust 高性能生成引擎 |
| **[nonebot-plugin-memes](https://github.com/MemeCrafters/nonebot-plugin-memes)** | `latest` | 🎨 模板资源和算法参考 |
| **[AstrBot](https://github.com/Soulter/AstrBot)** | `3.5+` | 🤖 机器人框架和平台适配 |
| **Pillow** | `10.0+` | 🖼️ 图像处理库 |
| **aiohttp** | `3.8+` | 🌐 异步HTTP客户端 |

#### 🏛️ 架构特点

- **🔄 异步处理** - 全异步设计，支持高并发
- **🧩 模块化设计** - 核心功能模块化，易于维护
- **💾 智能缓存** - 多层缓存机制，优化性能
- **🛡️ 错误恢复** - 完善的异常处理机制
- **⚡ 高性能** - Rust 底层引擎，毫秒级响应

</td>
<td width="50%">

#### 📁 项目结构

```
astrbot_plugin_meme_generator/
├── 📄 main.py                 # 插件主入口
├── 📁 config/                 # 配置管理
│   ├── __init__.py
│   └── settings.py
├── 📁 core/                   # 核心功能
│   ├── image_generator.py     # 图像生成器
│   ├── meme_manager.py        # 表情包管理器
│   ├── param_collector.py     # 参数收集器
│   └── template_manager.py    # 模板管理器
├── 📁 handlers/               # 命令处理器
│   ├── admin_handlers.py      # 管理员命令
│   ├── generation_handler.py  # 生成处理
│   └── template_handlers.py   # 模板命令
├── 📁 utils/                  # 工具模块
│   ├── avatar_cache.py        # 头像缓存
│   ├── cache_manager.py       # 缓存管理
│   ├── cooldown_manager.py    # 冷却管理
│   ├── image_utils.py         # 图像工具
│   ├── network_utils.py       # 网络工具
│   ├── permission_utils.py    # 权限工具
│   ├── platform_utils.py      # 平台工具
│   └── template_loader.py     # 模板加载器
├── 📁 static/                 # 静态资源
│   ├── css/                   # 样式文件
│   ├── html/                  # HTML模板
│   ├── data/                  # 数据文件
│   └── picture/               # 图片资源
└── 📄 requirements.txt        # 依赖列表
```

</td>
</tr>
</table>

### ⚡ 性能优化

<div align="center">

| 优化策略 | 实现方式 | 性能提升 |
|----------|----------|----------|
| **🚀 Rust 引擎** | meme-generator-rs | 10x 生成速度 |
| **💾 多级缓存** | 头像+模板+结果缓存 | 5x 响应速度 |
| **🔄 异步处理** | asyncio + aiohttp | 支持高并发 |
| **🧠 智能预加载** | 热门模板预缓存 | 减少首次延迟 |
| **📊 资源监控** | 内存+CPU实时监控 | 稳定性保障 |

</div>

## ❓ 常见问题解答

<div align="center">

### 🤔 遇到问题？这里有答案！

</div>

<details>
<summary>🚀 <strong>性能相关问题</strong></summary>

### Q: 首次启动很慢怎么办？
**A:** 首次启动需要下载字体和模板资源，这是正常现象。

**解决方案：**
- ✅ 确保网络连接稳定
- ✅ 耐心等待 2-3 分钟
- ✅ 后续启动会非常快速
- ✅ 可查看日志了解下载进度

### Q: 生成表情包很慢？
**A:** 可能是网络或配置问题。

**排查步骤：**
1. 检查网络连接是否稳定
2. 确认头像缓存是否启用
3. 调整 `generation_timeout` 配置
4. 查看系统资源使用情况

### Q: 插件占用资源过多？
**A:** 可以通过配置优化资源使用。

**优化建议：**
```json
{
  "enable_avatar_cache": false,    // 关闭头像缓存
  "cache_expire_hours": 6,         // 缩短缓存时间
  "cooldown_seconds": 5,           // 增加冷却时间
  "disabled_templates": ["大型模板"] // 禁用资源密集型模板
}
```

</details>

<details>
<summary>🔧 <strong>功能使用问题</strong></summary>

### Q: 如何添加自定义模板？
**A:** 目前暂不支持自定义模板，但在规划中。

**替代方案：**
- 🎨 使用现有 200+ 模板
- 📝 提交模板需求到 Issues
- 🔄 等待后续版本更新
- 🤝 参与开源贡献

### Q: 表情包生成失败？
**A:** 可能是多种原因导致。

**常见原因及解决：**
| 问题 | 原因 | 解决方案 |
|------|------|----------|
| 网络错误 | 无法下载头像 | 检查网络连接 |
| 参数错误 | 用户输入格式错误 | 查看 `表情信息` 了解正确格式 |
| 模板禁用 | 模板被管理员禁用 | 使用其他模板或联系管理员 |
| 系统错误 | 服务异常 | 查看日志或重启插件 |

### Q: 某些模板无法使用？
**A:** 可能被管理员禁用或模板本身有问题。

**检查方法：**
```bash
禁用列表              # 查看被禁用的模板
表情信息 模板名        # 查看模板详细信息
表情状态              # 查看插件整体状态
```

</details>

<details>
<summary>🛠️ <strong>技术问题</strong></summary>

### Q: 字体显示异常？
**A:** 通常是系统缺少中文字体。

**解决方案：** 参考上方 [字体问题解决](#️-字体问题解决) 章节

### Q: 插件无法启动？
**A:** 可能是依赖或配置问题。

**排查步骤：**
1. 检查 Python 版本 (需要 3.10+)
2. 安装所有依赖：`pip install -r requirements.txt`
3. 检查 AstrBot 版本兼容性
4. 查看启动日志错误信息

### Q: 如何查看详细日志？
**A:** 可以通过多种方式查看日志。

**日志位置：**
- AstrBot 主日志：`logs/astrbot.log`
- 插件专用日志：通过 `表情状态` 命令查看
- 系统日志：根据部署方式而定

</details>

<details>
<summary>🔒 <strong>权限和安全问题</strong></summary>

### Q: 普通用户无法使用？
**A:** 检查插件是否启用和权限配置。

**检查项目：**
- 插件全局开关是否启用
- 用户是否在冷却期内
- 是否有特殊权限限制

### Q: 如何设置管理员？
**A:** 在 AstrBot 主配置中设置。

**设置方法：**
1. 打开 AstrBot 管理面板
2. 进入 "基础配置" → "管理员设置"
3. 添加用户ID到管理员列表
4. 重启 AstrBot 生效

</details>

## 🤝 参与贡献

<div align="center">

### 💡 让我们一起让这个项目更好！

</div>

我们欢迎所有形式的贡献，无论是代码、文档、建议还是 Bug 报告！

<table>
<tr>
<td width="50%">

#### 🐛 报告问题

**发现 Bug？**
1. 🔍 先搜索现有 Issues
2. 📝 使用 Issue 模板
3. 🔬 提供详细复现步骤
4. 📋 附上系统环境信息

**功能建议？**
1. 💭 描述使用场景
2. 🎯 说明期望效果
3. 🤔 考虑实现难度
4. 📊 评估用户需求

</td>
<td width="50%">

#### 💻 代码贡献

**提交代码？**
1. 🍴 Fork 项目到你的账户
2. 🌿 创建功能分支
3. ✨ 编写代码和测试
4. 📤 提交 Pull Request

**代码规范：**
- 🐍 遵循 PEP 8 规范
- 📝 添加必要的注释
- 🧪 编写单元测试
- 📚 更新相关文档

</td>
</tr>
</table>

### 🛠️ 开发环境搭建

<details>
<summary>🔧 <strong>点击查看详细开发指南</strong></summary>

#### 1️⃣ 环境准备

```bash
# 检查 Python 版本 (需要 3.10+)
python --version

# 克隆项目
git clone http://127.0.0.1:3000/SodaSizzle/astrbot_plugin_meme_generator
cd astrbot_plugin_meme_generator

# 创建虚拟环境 (推荐)
python -m venv venv
source venv/bin/activate  # Linux/Mac
# 或
venv\Scripts\activate     # Windows
```

#### 2️⃣ 依赖安装

```bash
# 安装开发依赖
pip install -r requirements.txt
pip install -r requirements-dev.txt  # 如果有开发专用依赖

# 安装预提交钩子
pre-commit install
```

#### 3️⃣ 开发测试

```bash
# 运行单元测试
python -m pytest tests/ -v

# 代码格式检查
black --check .
flake8 .

# 类型检查
mypy .

# 运行所有检查
pre-commit run --all-files
```

#### 4️⃣ 调试运行

```bash
# 在 AstrBot 环境中测试
cd /path/to/AstrBot
python main.py

# 查看插件日志
tail -f logs/astrbot.log | grep meme
```

</details>

### 📋 开发规范

<div align="center">

| 类型 | 规范 | 工具 |
|------|------|------|
| **代码风格** | PEP 8 | `black`, `flake8` |
| **类型注解** | 强制要求 | `mypy` |
| **测试覆盖** | >80% | `pytest`, `coverage` |
| **文档字符串** | Google 风格 | `pydocstyle` |
| **提交信息** | Conventional Commits | `commitizen` |

</div>

## 📄 开源许可

<div align="center">

[![License: MIT](https://img.shields.io/badge/License-MIT-yellow.svg?style=for-the-badge)](https://opensource.org/licenses/MIT)

</div>

本项目采用 **MIT 许可证** 开源，这意味着：

- ✅ **商业使用** - 可用于商业项目
- ✅ **修改分发** - 可自由修改和分发
- ✅ **私人使用** - 可用于个人项目
- ✅ **专利使用** - 包含专利使用权

**唯一要求：** 保留原始许可证和版权声明

查看 [LICENSE](LICENSE) 文件了解完整详情。

## 📞 联系我们

<div align="center">

### 🌟 项目信息

</div>

<table>
<tr>
<td width="50%">

#### 👨‍💻 开发者信息

- **👤 作者**: SodaSizzle
- **📧 邮箱**: [联系邮箱]
- **🐙 GitHub**: [@SodaSizzle](https://github.com/SodaSizzle)
- **💬 QQ群**: [加入讨论群]

#### 📊 项目状态

- **🏷️ 当前版本**: v1.0.0
- **🔄 更新频率**: 持续维护
- **🐛 Bug 修复**: 及时响应
- **✨ 新功能**: 定期发布

</td>
<td width="50%">

#### 🔗 相关链接

- **📦 项目仓库**: [GitHub](http://127.0.0.1:3000/SodaSizzle/astrbot_plugin_meme_generator)
- **📚 使用文档**: [Wiki](https://github.com/SodaSizzle/astrbot_plugin_meme_generator/wiki)
- **🐛 问题反馈**: [Issues](https://github.com/SodaSizzle/astrbot_plugin_meme_generator/issues)
- **💡 功能建议**: [Discussions](https://github.com/SodaSizzle/astrbot_plugin_meme_generator/discussions)

#### 🤝 社区支持

- **📖 使用教程**: 详细的使用指南
- **❓ 问题解答**: 社区互助答疑
- **🔄 版本更新**: 及时的更新通知
- **🎉 活动交流**: 定期的社区活动

</td>
</tr>
</table>

## ❤️ 特别致谢

<div align="center">

### 🙏 感谢所有让这个项目成为可能的开源项目和贡献者

</div>

<table>
<tr>
<td width="33%">

#### 🦀 核心引擎
**[meme-generator-rs](https://github.com/MemeCrafters/meme-generator-rs)**
- 🚀 高性能 Rust 生成引擎
- ⚡ 毫秒级响应速度
- 🎨 丰富的模板支持

</td>
<td width="33%">

#### 🎭 模板资源
**[nonebot-plugin-memes](https://github.com/MemeCrafters/nonebot-plugin-memes)**
- 📚 丰富的模板库
- 🧠 智能算法参考
- 🎯 用户体验设计

</td>
<td width="33%">

#### 🤖 框架支持
**[AstrBot](https://github.com/Soulter/AstrBot)**
- 🏗️ 优秀的机器人框架
- 🔌 完善的插件系统
- 🌐 多平台适配支持

</td>
</tr>
</table>

### 🌟 贡献者

感谢所有为这个项目做出贡献的开发者！

<div align="center">

[![Contributors](https://contrib.rocks/image?repo=SodaSizzle/astrbot_plugin_meme_generator)](https://github.com/SodaSizzle/astrbot_plugin_meme_generator/graphs/contributors)

</div>

---

<div align="center">

## 🎭 让聊天更有趣，让表情包飞一会儿！

### ⭐ 如果这个插件对你有帮助，请给个 Star 支持一下！

[![Star History Chart](https://api.star-history.com/svg?repos=SodaSizzle/astrbot_plugin_meme_generator&type=Date)](https://star-history.com/#SodaSizzle/astrbot_plugin_meme_generator&Date)

**🚀 让我们一起让聊天变得更加有趣！**

</div>

