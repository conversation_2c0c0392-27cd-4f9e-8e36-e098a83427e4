/* Meme表情状态页面样式 */

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Microsoft YaHei', 'PingFang SC', Arial, sans-serif;
    font-size: 20px;
    background: white;
}

.container {
    width: 100%;
    background: white;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
    overflow: hidden;
    margin: 0 auto;
}

.header {
    background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
    color: white;
    padding: 40px 30px;
    text-align: center;
    position: relative;
}

.header::before {
    content: '';
    position: absolute;
    inset: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><circle cx="20" cy="20" r="2" fill="rgba(255,255,255,0.1)"/><circle cx="80" cy="40" r="3" fill="rgba(255,255,255,0.1)"/><circle cx="40" cy="80" r="2" fill="rgba(255,255,255,0.1)"/></svg>');
}

.header h1, .header .subtitle {
    position: relative;
    z-index: 1;
}

.header h1 {
    font-size: 3.2em;
    margin-bottom: 10px;
}

.subtitle {
    font-size: 1.4em;
    opacity: 0.9;
}

.content {
    padding: 40px 30px;
}

.section-title {
    font-size: 1.8em;
    margin-bottom: 25px;
    color: #333;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 10px;
}

/* 状态卡片样式 */
.status-section {
    margin-bottom: 45px;
}

.status-grid {
    display: flex;
    flex-wrap: wrap;
    gap: 20px;
}

.status-card {
    flex: 1;
    min-width: 280px;
    display: flex;
    align-items: center;
    padding: 25px;
    border-radius: 12px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    transition: transform 0.2s ease;
}

.status-card:hover {
    transform: translateY(-2px);
}

.status-card.enabled {
    background: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);
    border-left: 4px solid #27ae60;
}

.status-card.disabled {
    background: linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%);
    border-left: 4px solid #e74c3c;
}

.status-icon {
    font-size: 2.8em;
    margin-right: 20px;
}

.status-info {
    flex: 1;
}

.status-label {
    font-size: 1.0em;
    color: #666;
    margin-bottom: 8px;
    font-weight: 500;
}

.status-value {
    font-size: 1.5em;
    font-weight: 700;
    color: #333;
}

/* 配置参数样式 */
.config-section {
    margin-bottom: 45px;
}

.config-grid {
    display: flex;
    flex-wrap: wrap;
    gap: 20px;
}

.config-item {
    flex: 1;
    min-width: 160px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 25px 20px;
    border-radius: 12px;
    text-align: center;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    transition: transform 0.2s ease;
}

.config-item:hover {
    transform: translateY(-2px);
}

.config-label {
    font-size: 1.0em;
    margin-bottom: 10px;
    opacity: 0.9;
    font-weight: 500;
}

.config-value {
    font-size: 1.6em;
    font-weight: 700;
}

/* 统计信息样式 */
.stats-section {
    margin-bottom: 40px;
}

.stats-grid {
    display: flex;
    flex-wrap: wrap;
    gap: 20px;
}

.stat-card {
    flex: 1;
    min-width: 180px;
    background: linear-gradient(135deg, #ff6b6b, #feca57);
    color: white;
    padding: 30px 25px;
    border-radius: 12px;
    text-align: center;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    transition: transform 0.2s ease;
}

.stat-card:hover {
    transform: translateY(-2px);
}

.stat-number {
    font-size: 3.2em;
    font-weight: 700;
    margin-bottom: 10px;
}

.stat-label {
    font-size: 1.2em;
    opacity: 0.9;
    font-weight: 500;
}

.footer {
    background: #f8f9fa;
    color: #666;
    text-align: center;
    padding: 25px;
    font-size: 1.0em;
    border-top: 1px solid #eee;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .header h1 {
        font-size: 2.4em;
    }

    .content {
        padding: 30px 20px;
    }

    .status-grid,
    .config-grid,
    .stats-grid {
        flex-direction: column;
    }

    .status-card,
    .config-item,
    .stat-card {
        min-width: auto;
    }

    .status-icon {
        font-size: 2.4em;
    }
}
