<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Meme 表情包插件 - 帮助菜单</title>
    <link rel="stylesheet" href="../css/meme_help.css">
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🎭 Meme 表情包插件</h1>
            <div class="subtitle">高性能表情包生成器 - 让聊天更有趣！</div>
        </div>

        <div class="content">
            <div class="section">
                <h2 class="section-title">📋 基础命令</h2>
                <div class="command-grid">
                    {% for cmd in basic_commands %}
                    <div class="command-card">
                        <div class="command-name">
                            <span class="emoji">{{ cmd.emoji }}</span>{{ cmd.name }}
                        </div>
                        <div class="command-desc">{{ cmd.desc }}</div>
                    </div>
                    {% endfor %}
                </div>
            </div>

            <div class="section admin-section">
                <h2 class="section-title">🔧 管理员命令</h2>
                <div class="command-grid">
                    {% for cmd in admin_commands %}
                    <div class="command-card">
                        <div class="command-name">
                            <span class="emoji">{{ cmd.emoji }}</span>{{ cmd.name }}
                        </div>
                        <div class="command-desc">{{ cmd.desc }}</div>
                    </div>
                    {% endfor %}
                </div>
            </div>

            <div class="usage-tips">
                <h3>💡 使用小贴士</h3>
                <ul>
                    <li>直接发送表情包关键词即可生成，如："加载中"、"挠头"</li>
                    <li>支持@用户自动获取头像，也可以上传图片</li>
                    <li>可以引用他人消息来使用其头像和昵称</li>
                    <li>命令前缀默认为/，也可直接@</li>
                </ul>
            </div>
        </div>

        <div class="footer">
            <p>🌟 AstrBot Meme Generator Plugin {{ version }} by {{ author }}</p>
        </div>
    </div>
</body>
</html>
